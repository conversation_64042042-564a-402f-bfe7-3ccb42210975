import 'dart:convert';
import 'package:http/http.dart' as http;
import '../config/api_config.dart';
import '../models/user.dart';
import '../services/api_service.dart';

class UserService {
  final ApiService _apiService = ApiService();

  Future<Map<String, String>> _getHeaders() async {
    await _apiService.initializeToken();
    final token = _apiService.token;
    return {
      'Content-Type': 'application/json',
      'Authorization': 'Bearer $token',
    };
  }

  // Get all users with filtering and pagination
  Future<UserListResponse> getUsers(UserQuery query) async {
    try {
      await _apiService.initializeToken();
      final queryParams = query.toQueryParameters();
      final response = await _apiService.dio.get('/users', queryParameters: queryParams);

      // 如果API直接返回用户列表数组，包装成UserListResponse格式
      if (response.data is List) {
        final users = (response.data as List).map((json) => User.fromJson(json)).toList();
        return UserListResponse(
          users: users,
          totalCount: users.length,
          pageNumber: query.pageNumber,
          pageSize: query.pageSize,
          totalPages: 1,
        );
      } else {
        // 如果API返回的是分页格式，直接使用
        return UserListResponse.fromJson(response.data);
      }
    } catch (e) {
      throw Exception('获取用户列表失败: $e');
    }
  }

  // Get user by ID
  Future<User> getUserById(int id) async {
    try {
      await _apiService.initializeToken();
      final response = await _apiService.dio.get('/users/$id');
      return User.fromJson(response.data);
    } catch (e) {
      throw Exception('获取用户信息失败: $e');
    }
  }

  // Create new user
  Future<User> createUser(CreateUserRequest request) async {
    try {
      await _apiService.initializeToken();
      final response = await _apiService.dio.post('/users', data: request.toJson());
      return User.fromJson(response.data);
    } catch (e) {
      throw Exception('创建用户失败: $e');
    }
  }

  // Update user
  Future<User> updateUser(int id, UpdateUserRequest request) async {
    try {
      await _apiService.initializeToken();
      final response = await _apiService.dio.put('/users/$id', data: request.toJson());
      return User.fromJson(response.data);
    } catch (e) {
      throw Exception('更新用户失败: $e');
    }
  }

  // Delete user
  Future<void> deleteUser(int id) async {
    try {
      await _apiService.initializeToken();
      await _apiService.dio.delete('/users/$id');
    } catch (e) {
      throw Exception('删除用户失败: $e');
    }
  }

  // Change user password
  Future<void> changePassword(int id, ChangePasswordRequest request) async {
    try {
      await _apiService.initializeToken();
      await _apiService.dio.post('/users/$id/change-password', data: request.toJson());
    } catch (e) {
      throw Exception('修改密码失败: $e');
    }
  }

  // Get all roles
  Future<List<String>> getRoles() async {
    try {
      await _apiService.initializeToken();
      final response = await _apiService.dio.get('/users/roles');
      return (response.data as List).cast<String>();
    } catch (e) {
      throw Exception('获取角色列表失败: $e');
    }
  }

  // Get all departments
  Future<List<String>> getDepartments() async {
    try {
      await _apiService.initializeToken();
      final response = await _apiService.dio.get('/users/departments');
      return (response.data as List).cast<String>();
    } catch (e) {
      throw Exception('获取部门列表失败: $e');
    }
  }

  // Change user role
  Future<void> changeUserRole(int id, String role) async {
    try {
      await _apiService.initializeToken();
      final request = ChangeRoleRequest(role: role);
      await _apiService.dio.post('/users/$id/change-role', data: request.toJson());
    } catch (e) {
      // 检查是否是token相关的错误
      if (e.toString().contains('Invalid user token') || 
          e.toString().contains('401')) {
        throw Exception('登录已过期，请重新登录');
      }
      throw Exception('修改用户权限失败: $e');
    }
  }

  // Toggle user status (ban/unban)
  Future<void> toggleUserStatus(int id, bool isActive) async {
    try {
      await _apiService.initializeToken();
      final request = ToggleStatusRequest(isActive: isActive);
      await _apiService.dio.post('/users/$id/toggle-status', data: request.toJson());
    } catch (e) {
      // 检查是否是token相关的错误
      if (e.toString().contains('Invalid user token') || 
          e.toString().contains('401')) {
        throw Exception('登录已过期，请重新登录');
      }
      throw Exception('修改用户状态失败: $e');
    }
  }

  // Check if username exists
  Future<bool> checkUsername(String username, {int? excludeId}) async {
    try {
      await _apiService.initializeToken();
      final queryParams = excludeId != null ? {'excludeId': excludeId.toString()} : <String, dynamic>{};
      final response = await _apiService.dio.get('/users/check-username/$username', queryParameters: queryParams);
      return response.data['exists'] ?? false;
    } catch (e) {
      return false;
    }
  }

  // Check if email exists
  Future<bool> checkEmail(String email, {int? excludeId}) async {
    try {
      await _apiService.initializeToken();
      final queryParams = excludeId != null ? {'excludeId': excludeId.toString()} : <String, dynamic>{};
      final response = await _apiService.dio.get('/users/check-email/$email', queryParameters: queryParams);
      return response.data['exists'] ?? false;
    } catch (e) {
      return false;
    }
  }

  // Update current user's profile (self-update)
  Future<User> updateProfile(UpdateProfileRequest request) async {
    try {
      await _apiService.initializeToken();
      final response = await _apiService.dio.put('/users/profile', data: request.toJson());
      return User.fromJson(response.data);
    } catch (e) {
      throw Exception('更新个人信息失败: $e');
    }
  }

  // Change current user's password (self-change)
  Future<void> changeMyPassword(ChangePasswordRequest request) async {
    try {
      await _apiService.initializeToken();
      await _apiService.dio.post('/users/profile/change-password', data: request.toJson());
    } catch (e) {
      throw Exception('修改密码失败: $e');
    }
  }
}