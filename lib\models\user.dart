class User {
  final int id;
  final String username;
  final String email;
  final String role;
  final String? fullName;
  final String? department;
  final bool isActive;
  final DateTime createdAt;
  final DateTime updatedAt;
  final int assignedAssetsCount;

  User({
    required this.id,
    required this.username,
    required this.email,
    required this.role,
    this.fullName,
    this.department,
    required this.isActive,
    required this.createdAt,
    required this.updatedAt,
    required this.assignedAssetsCount,
  });

  factory User.fromJson(Map<String, dynamic> json) {
    return User(
      id: json['id'] ?? 0,
      username: json['username'] ?? '',
      email: json['email'] ?? '',
      role: json['role'] ?? '',
      fullName: json['fullName'],
      department: json['department'],
      isActive: json['isActive'] ?? true,
      createdAt: DateTime.parse(json['createdAt'] ?? DateTime.now().toIso8601String()),
      updatedAt: DateTime.parse(json['updatedAt'] ?? DateTime.now().toIso8601String()),
      assignedAssetsCount: json['assignedAssetsCount'] ?? 0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'username': username,
      'email': email,
      'role': role,
      'fullName': fullName,
      'department': department,
      'isActive': isActive,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
      'assignedAssetsCount': assignedAssetsCount,
    };
  }

  User copyWith({
    int? id,
    String? username,
    String? email,
    String? role,
    String? fullName,
    String? department,
    bool? isActive,
    DateTime? createdAt,
    DateTime? updatedAt,
    int? assignedAssetsCount,
  }) {
    return User(
      id: id ?? this.id,
      username: username ?? this.username,
      email: email ?? this.email,
      role: role ?? this.role,
      fullName: fullName ?? this.fullName,
      department: department ?? this.department,
      isActive: isActive ?? this.isActive,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      assignedAssetsCount: assignedAssetsCount ?? this.assignedAssetsCount,
    );
  }

  bool get isAdmin => role == UserRole.admin;
  bool get isNormalUser => role == UserRole.user;
}

// 用户角色枚举
class UserRole {
  static const String admin = 'Admin';
  static const String user = 'Normal User';
  
  static List<String> get all => [admin, user];
}

// 登录响应模型
class LoginResponse {
  final String token;
  final User user;

  LoginResponse({
    required this.token,
    required this.user,
  });

  factory LoginResponse.fromJson(Map<String, dynamic> json) {
    return LoginResponse(
      token: json['token'],
      user: User.fromJson(json['user']),
    );
  }
}

class CreateUserRequest {
  final String username;
  final String email;
  final String password;
  final String role;
  final String? fullName;
  final String? department;

  CreateUserRequest({
    required this.username,
    required this.email,
    required this.password,
    required this.role,
    this.fullName,
    this.department,
  });

  Map<String, dynamic> toJson() {
    return {
      'username': username,
      'email': email,
      'password': password,
      'role': role,
      'fullName': fullName,
      'department': department,
    };
  }
}

class UpdateUserRequest {
  final String? username;
  final String? email;
  final String? role;
  final String? fullName;
  final String? department;

  UpdateUserRequest({
    this.username,
    this.email,
    this.role,
    this.fullName,
    this.department,
  });

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = {};
    if (username != null) data['username'] = username;
    if (email != null) data['email'] = email;
    if (role != null) data['role'] = role;
    if (fullName != null) data['fullName'] = fullName;
    if (department != null) data['department'] = department;
    return data;
  }
}

class ChangePasswordRequest {
  final String currentPassword;
  final String newPassword;

  ChangePasswordRequest({
    required this.currentPassword,
    required this.newPassword,
  });

  Map<String, dynamic> toJson() {
    return {
      'currentPassword': currentPassword,
      'newPassword': newPassword,
    };
  }
}

class UserListResponse {
  final List<User> users;
  final int totalCount;
  final int pageNumber;
  final int pageSize;
  final int totalPages;

  UserListResponse({
    required this.users,
    required this.totalCount,
    required this.pageNumber,
    required this.pageSize,
    required this.totalPages,
  });

  factory UserListResponse.fromJson(Map<String, dynamic> json) {
    return UserListResponse(
      users: (json['users'] as List<dynamic>?)
          ?.map((item) => User.fromJson(item as Map<String, dynamic>))
          .toList() ?? [],
      totalCount: json['totalCount'] ?? 0,
      pageNumber: json['pageNumber'] ?? 1,
      pageSize: json['pageSize'] ?? 10,
      totalPages: json['totalPages'] ?? 0,
    );
  }
}

class UserQuery {
  final String? search;
  final String? role;
  final String? department;
  final int pageNumber;
  final int pageSize;
  final String? sortBy;
  final bool sortDescending;

  UserQuery({
    this.search,
    this.role,
    this.department,
    this.pageNumber = 1,
    this.pageSize = 10,
    this.sortBy = 'CreatedAt',
    this.sortDescending = true,
  });

  Map<String, dynamic> toQueryParameters() {
    final Map<String, dynamic> params = {
      'pageNumber': pageNumber.toString(),
      'pageSize': pageSize.toString(),
      'sortDescending': sortDescending.toString(),
    };
    
    if (search != null && search!.isNotEmpty) {
      params['search'] = search!;
    }
    if (role != null && role!.isNotEmpty) {
      params['role'] = role!;
    }
    if (department != null && department!.isNotEmpty) {
      params['department'] = department!;
    }
    if (sortBy != null && sortBy!.isNotEmpty) {
      params['sortBy'] = sortBy!;
    }
    
    return params;
  }
}

class ChangeRoleRequest {
  final String role;

  ChangeRoleRequest({required this.role});

  Map<String, dynamic> toJson() {
    return {'role': role};
  }
}

class UpdateProfileRequest {
  final String? email;
  final String? fullName;
  final String? department;

  UpdateProfileRequest({
    this.email,
    this.fullName,
    this.department,
  });

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = {};
    if (email != null) data['email'] = email;
    if (fullName != null) data['fullName'] = fullName;
    if (department != null) data['department'] = department;
    return data;
  }
}

class ToggleStatusRequest {
  final bool isActive;

  ToggleStatusRequest({required this.isActive});

  Map<String, dynamic> toJson() {
    return {'isActive': isActive};
  }
}
